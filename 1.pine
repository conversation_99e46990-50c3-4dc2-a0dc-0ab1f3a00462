// 此Pine Script™代码受Mozilla公共许可证2.0条款约束，详见 https://mozilla.org/MPL/2.0/
// © QuantAlgo - 高级版本

//@version=6
strategy(title="自适应趋势流高级策略 [QuantAlgo Pro]", overlay=true, max_labels_count=500, 
         default_qty_type=strategy.percent_of_equity, default_qty_value=100,
         initial_capital=10000, currency=currency.USD, commission_type=strategy.commission.percent, 
         commission_value=0.1, slippage=2, calc_on_every_tick=false, calc_on_order_fills=true)

//              ╔════════════════════════════════╗              //
//              ║        用户自定义设置          ║              //
//              ╚════════════════════════════════╝              //

// 输入组
var string trend_settings      = "════════ 趋势设置 ════════"
var string signal_settings     = "════════ 信号过滤 ════════"
var string risk_settings       = "════════ 风险管理 ════════"
var string position_settings   = "════════ 仓位管理 ════════"
var string time_settings       = "════════ 时间过滤 ════════"
var string visual_settings     = "════════ 可视化设置 ════════"

// 趋势设置
length        = input.int(10, "主要长度", minval=2, maxval=100, group=trend_settings)
smooth_len    = input.int(14, "平滑长度", minval=2, maxval=50, group=trend_settings)
sensitivity   = input.float(2.0, "敏感度", step=0.1, minval=0.5, maxval=5.0, group=trend_settings)

// 信号过滤设置
use_volume_filter    = input.bool(true, "启用成交量过滤", group=signal_settings)
volume_ma_length     = input.int(20, "成交量均线长度", minval=5, maxval=100, group=signal_settings)
volume_multiplier    = input.float(1.2, "成交量倍数", step=0.1, minval=1.0, maxval=3.0, group=signal_settings)

use_rsi_filter       = input.bool(true, "启用RSI过滤", group=signal_settings)
rsi_length           = input.int(14, "RSI长度", minval=5, maxval=50, group=signal_settings)
rsi_oversold         = input.int(30, "RSI超卖线", minval=10, maxval=40, group=signal_settings)
rsi_overbought       = input.int(70, "RSI超买线", minval=60, maxval=90, group=signal_settings)

use_macd_filter      = input.bool(false, "启用MACD过滤", group=signal_settings)
macd_fast            = input.int(12, "MACD快线", minval=5, maxval=20, group=signal_settings)
macd_slow            = input.int(26, "MACD慢线", minval=20, maxval=50, group=signal_settings)
macd_signal          = input.int(9, "MACD信号线", minval=5, maxval=15, group=signal_settings)

// 风险管理设置
risk_mode            = input.string("固定百分比", "风险模式", 
                      options=["固定百分比", "ATR动态", "波动率调整"], group=risk_settings)
use_sl               = input.bool(true, "使用止损", group=risk_settings)
use_tp               = input.bool(true, "使用止盈", group=risk_settings)
sl_pct               = input.float(3.0, "固定止损百分比 (%)", 
                      minval=0.5, maxval=20.0, step=0.1, group=risk_settings)
tp_pct               = input.float(6.0, "固定止盈百分比 (%)", 
                      minval=1.0, maxval=50.0, step=0.1, group=risk_settings)

use_trailing_sl      = input.bool(true, "启用移动止损", group=risk_settings)
trailing_sl_pct      = input.float(2.0, "移动止损百分比 (%)", 
                      minval=0.5, maxval=10.0, step=0.1, group=risk_settings)

atr_length           = input.int(14, "ATR长度", minval=5, maxval=50, group=risk_settings)
atr_multiplier_sl    = input.float(2.0, "ATR止损倍数", step=0.1, minval=1.0, maxval=5.0, group=risk_settings)
atr_multiplier_tp    = input.float(3.0, "ATR止盈倍数", step=0.1, minval=1.0, maxval=8.0, group=risk_settings)

max_drawdown_pct     = input.float(15.0, "最大回撤限制 (%)", 
                      minval=5.0, maxval=50.0, step=1.0, group=risk_settings)
max_consecutive_loss = input.int(3, "最大连续亏损次数", minval=1, maxval=10, group=risk_settings)

// 仓位管理设置
position_mode        = input.string("固定仓位", "仓位模式", 
                      options=["固定仓位", "风险平价", "凯利公式", "波动率调整"], group=position_settings)
base_position_pct    = input.float(100.0, "基础仓位百分比 (%)", 
                      minval=10.0, maxval=100.0, step=5.0, group=position_settings)
max_position_pct     = input.float(100.0, "最大仓位百分比 (%)", 
                      minval=10.0, maxval=100.0, step=5.0, group=position_settings)

use_pyramiding       = input.bool(false, "启用加仓", group=position_settings)
pyramid_levels       = input.int(2, "加仓层数", minval=1, maxval=5, group=position_settings)
pyramid_scale        = input.float(0.5, "加仓比例", step=0.1, minval=0.1, maxval=1.0, group=position_settings)

// 时间过滤设置
start_date           = input.time(timestamp("2020-01-01 00:00"), "开始日期", group=time_settings)
end_date             = input.time(timestamp("2025-12-31 23:59"), "结束日期", group=time_settings)
trade_long           = input.bool(true, "允许做多", group=time_settings)
trade_short          = input.bool(true, "允许做空", group=time_settings)

use_session_filter   = input.bool(false, "启用交易时段过滤", group=time_settings)
trading_session      = input.session("0930-1600", "交易时段", group=time_settings)

// 可视化设置
bullcolor            = input.color(#00ffaa, "看涨颜色", group=visual_settings)
bearcolor            = input.color(#ff0000, "看跌颜色", group=visual_settings)
showbars             = input.bool(true, "K线着色", group=visual_settings)
showbg               = input.bool(true, "背景着色", group=visual_settings)
showsignals          = input.bool(true, "显示信号", group=visual_settings)
show_statistics      = input.bool(true, "显示统计面板", group=visual_settings)
show_risk_panel      = input.bool(true, "显示风险面板", group=visual_settings)

//              ╔════════════════════════════════╗              //
//              ║        核心计算函数            ║              //
//              ╚════════════════════════════════╝              //

// 趋势计算
calculate_trend_levels() =>
    typical = hlc3
    fast_ema = ta.ema(typical, length)
    slow_ema = ta.ema(typical, length * 2)
    basis = (fast_ema + slow_ema) / 2
    
    vol = ta.stdev(typical, length)
    smooth_vol = ta.ema(vol, smooth_len)
    
    upper = basis + (smooth_vol * sensitivity)
    lower = basis - (smooth_vol * sensitivity)
    
    [basis, upper, lower]

get_trend_state(upper, lower, basis) =>
    var float prev_level = na
    var int trend = 0
    
    if na(prev_level)
        trend := close > basis ? 1 : -1
        prev_level := trend == 1 ? lower : upper
    
    if trend == 1
        if close < lower
            trend := -1
            prev_level := upper
        else
            prev_level := lower
    else
        if close > upper
            trend := 1
            prev_level := lower
        else
            prev_level := upper
    
    [trend, prev_level]

// 信号过滤函数
volume_filter_passed() =>
    if not use_volume_filter
        true
    else
        volume_ma = ta.sma(volume, volume_ma_length)
        volume > volume_ma * volume_multiplier

rsi_filter_passed(is_long) =>
    if not use_rsi_filter
        true
    else
        rsi_val = ta.rsi(close, rsi_length)
        if is_long
            rsi_val < rsi_overbought
        else
            rsi_val > rsi_oversold

macd_filter_passed(is_long) =>
    if not use_macd_filter
        true
    else
        [macd_line, signal_line, _] = ta.macd(close, macd_fast, macd_slow, macd_signal)
        if is_long
            macd_line > signal_line
        else
            macd_line < signal_line

// 风险计算函数
calculate_position_size() =>
    equity = strategy.equity
    base_size = equity * base_position_pct / 100
    
    if position_mode == "固定仓位"
        base_size
    else if position_mode == "波动率调整"
        atr_val = ta.atr(atr_length)
        volatility_adj = math.min(2.0, math.max(0.5, 1.0 / (atr_val / close * 100)))
        base_size * volatility_adj
    else if position_mode == "风险平价"
        atr_val = ta.atr(atr_length)
        risk_per_share = atr_val * atr_multiplier_sl
        max_risk = equity * sl_pct / 100
        math.min(base_size, max_risk / risk_per_share)
    else
        base_size

calculate_stop_loss(entry_price, is_long) =>
    if risk_mode == "固定百分比"
        if is_long
            entry_price * (1 - sl_pct / 100)
        else
            entry_price * (1 + sl_pct / 100)
    else if risk_mode == "ATR动态"
        atr_val = ta.atr(atr_length)
        if is_long
            entry_price - (atr_val * atr_multiplier_sl)
        else
            entry_price + (atr_val * atr_multiplier_sl)
    else // 波动率调整
        volatility = ta.stdev(close, 20) / close
        adj_sl_pct = sl_pct * (1 + volatility * 10)
        if is_long
            entry_price * (1 - adj_sl_pct / 100)
        else
            entry_price * (1 + adj_sl_pct / 100)

calculate_take_profit(entry_price, is_long) =>
    if risk_mode == "固定百分比"
        if is_long
            entry_price * (1 + tp_pct / 100)
        else
            entry_price * (1 - tp_pct / 100)
    else if risk_mode == "ATR动态"
        atr_val = ta.atr(atr_length)
        if is_long
            entry_price + (atr_val * atr_multiplier_tp)
        else
            entry_price - (atr_val * atr_multiplier_tp)
    else // 波动率调整
        volatility = ta.stdev(close, 20) / close
        adj_tp_pct = tp_pct * (1 + volatility * 5)
        if is_long
            entry_price * (1 + adj_tp_pct / 100)
        else
            entry_price * (1 - adj_tp_pct / 100)

//              ╔════════════════════════════════╗              //
//              ║        策略状态管理            ║              //
//              ╚════════════════════════════════╝              //

// 全局变量
var int consecutive_losses = 0
var float max_equity = 0.0
var float entry_price = 0.0
var float current_sl = 0.0
var float current_tp = 0.0
var int pyramid_count = 0

// 更新最大权益
max_equity := math.max(max_equity, strategy.equity)

// 计算当前回撤
current_drawdown = (max_equity - strategy.equity) / max_equity * 100

// 检查风险限制
risk_limits_ok = current_drawdown < max_drawdown_pct and consecutive_losses < max_consecutive_loss

//              ╔════════════════════════════════╗              //
//              ║        主要策略逻辑            ║              //
//              ╚════════════════════════════════╝              //

[basis, upper, lower] = calculate_trend_levels()
[trend, level] = get_trend_state(upper, lower, basis)

// 时间过滤
in_date_range = time >= start_date and time <= end_date
in_session = use_session_filter ? time(timeframe.period, trading_session) : true

// 基础信号
long_signal = trend == 1 and trend[1] == -1
short_signal = trend == -1 and trend[1] == 1

// 应用过滤器
long_signal_filtered = long_signal and volume_filter_passed() and rsi_filter_passed(true) and macd_filter_passed(true)
short_signal_filtered = short_signal and volume_filter_passed() and 
                        rsi_filter_passed(false) and macd_filter_passed(false)

// 最终入场条件
long_condition = long_signal_filtered and trade_long and in_date_range and 
                 in_session and risk_limits_ok and strategy.position_size == 0
short_condition = short_signal_filtered and trade_short and in_date_range and 
                  in_session and risk_limits_ok and strategy.position_size == 0

// 加仓条件
pyramid_long_condition = use_pyramiding and strategy.position_size > 0 and pyramid_count < pyramid_levels and 
                        close > strategy.position_avg_price * 1.02 and long_signal_filtered
pyramid_short_condition = use_pyramiding and strategy.position_size < 0 and pyramid_count < pyramid_levels and 
                         close < strategy.position_avg_price * 0.98 and short_signal_filtered

//              ╔════════════════════════════════╗              //
//              ║        交易执行逻辑            ║              //
//              ╚════════════════════════════════╝              //

// 计算仓位大小
position_size = calculate_position_size()

// 主要入场
if long_condition
    entry_price := close
    current_sl := use_sl ? calculate_stop_loss(entry_price, true) : na
    current_tp := use_tp ? calculate_take_profit(entry_price, true) : na
    pyramid_count := 0
    
    strategy.entry("多头", strategy.long, qty=position_size/close, comment="多头入场")
    
    if use_sl and use_tp
        strategy.exit("多头出场", "多头", stop=current_sl, limit=current_tp, comment="多头SL/TP")
    else if use_sl
        strategy.exit("多头止损", "多头", stop=current_sl, comment="多头止损")
    else if use_tp
        strategy.exit("多头止盈", "多头", limit=current_tp, comment="多头止盈")

if short_condition
    entry_price := close
    current_sl := use_sl ? calculate_stop_loss(entry_price, false) : na
    current_tp := use_tp ? calculate_take_profit(entry_price, false) : na
    pyramid_count := 0
    
    strategy.entry("空头", strategy.short, qty=position_size/close, comment="空头入场")
    
    if use_sl and use_tp
        strategy.exit("空头出场", "空头", stop=current_sl, limit=current_tp, comment="空头SL/TP")
    else if use_sl
        strategy.exit("空头止损", "空头", stop=current_sl, comment="空头止损")
    else if use_tp
        strategy.exit("空头止盈", "空头", limit=current_tp, comment="空头止盈")

// 加仓逻辑
if pyramid_long_condition
    pyramid_size = position_size * pyramid_scale / close
    pyramid_count := pyramid_count + 1
    strategy.entry("多头加仓" + str.tostring(pyramid_count), strategy.long, qty=pyramid_size, comment="多头加仓")

if pyramid_short_condition
    pyramid_size = position_size * pyramid_scale / close
    pyramid_count := pyramid_count + 1
    strategy.entry("空头加仓" + str.tostring(pyramid_count), strategy.short, qty=pyramid_size, comment="空头加仓")

// 移动止损
if use_trailing_sl and strategy.position_size > 0
    new_sl = close * (1 - trailing_sl_pct / 100)
    if na(current_sl) or new_sl > current_sl
        current_sl := new_sl
        strategy.exit("多头移动止损", "多头", stop=current_sl, comment="移动止损")

if use_trailing_sl and strategy.position_size < 0
    new_sl = close * (1 + trailing_sl_pct / 100)
    if na(current_sl) or new_sl < current_sl
        current_sl := new_sl
        strategy.exit("空头移动止损", "空头", stop=current_sl, comment="移动止损")

// 趋势反转平仓
if strategy.position_size > 0 and short_signal_filtered
    strategy.close_all(comment="趋势反转平多")
    pyramid_count := 0

if strategy.position_size < 0 and long_signal_filtered
    strategy.close_all(comment="趋势反转平空")
    pyramid_count := 0

// 更新连续亏损计数
if strategy.closedtrades > strategy.closedtrades[1]
    if strategy.losstrades > strategy.losstrades[1]
        consecutive_losses := consecutive_losses + 1
    else
        consecutive_losses := 0

//              ╔════════════════════════════════╗              //
//              ║        可视化和面板            ║              //
//              ╚════════════════════════════════╝              //

// 绘制趋势线
p2 = plot(basis, color=trend == 1 ? bullcolor : bearcolor, linewidth=2, title="基准线")
p1 = plot(level, color=close > level ? bullcolor : bearcolor, linewidth=2, style=plot.style_linebr, title="趋势线")

// 信号标签
if showsignals and long_condition
    label.new(bar_index, low, "买入", color=bullcolor, textcolor=color.white, 
              style=label.style_label_up, size=size.large)

if showsignals and short_condition
    label.new(bar_index, high, "卖出", color=bearcolor, textcolor=color.white, 
              style=label.style_label_down, size=size.large)

if showsignals and pyramid_long_condition
    label.new(bar_index, low, "加仓", color=color.blue, textcolor=color.white, 
              style=label.style_label_up, size=size.small)

if showsignals and pyramid_short_condition
    label.new(bar_index, high, "加仓", color=color.purple, textcolor=color.white, 
              style=label.style_label_down, size=size.small)

// 背景着色
var float intensity = 0.0
if trend != trend[1]
    intensity := 0.0
intensity := trend == 1 ? math.min(intensity + 1, 20) : trend == -1 ? math.min(intensity + 1, 20) : intensity

color grad_color = trend == 1 ? 
     color.from_gradient(intensity, 0, 20, color.new(bullcolor, 95), color.new(bullcolor, 80)) :
     color.from_gradient(intensity, 0, 20, color.new(bearcolor, 95), color.new(bearcolor, 80))

bgcolor(showbg ? grad_color : na)
fill(p1, p2, color=trend == 1 ? color.new(bullcolor, 85) : color.new(bearcolor, 85))
barcolor(showbars ? (trend == 1 ? color.new(bullcolor, 15) : color.new(bearcolor, 15)) : na)

// 统计面板
if show_statistics and barstate.islast
    var table stats_table = table.new(position.top_right, 2, 12, bgcolor=color.white, border_width=1)
    
    table.cell(stats_table, 0, 0, "策略统计", text_color=color.white, text_size=size.normal, bgcolor=color.blue)
    table.cell(stats_table, 1, 0, "", text_color=color.white, text_size=size.normal, bgcolor=color.blue)
    
    // 基本信息
    table.cell(stats_table, 0, 1, "当前趋势", text_color=color.black, text_size=size.small)
    table.cell(stats_table, 1, 1, trend == 1 ? "看涨" : "看跌", 
               text_color=trend == 1 ? bullcolor : bearcolor, text_size=size.small)
    
    table.cell(stats_table, 0, 2, "当前持仓", text_color=color.black, text_size=size.small)
    position_text = strategy.position_size > 0 ? "做多" : strategy.position_size < 0 ? "做空" : "无持仓"
    table.cell(stats_table, 1, 2, position_text, 
               text_color=strategy.position_size > 0 ? bullcolor : strategy.position_size < 0 ? bearcolor : color.gray, 
               text_size=size.small)
    
    // 绩效指标
    table.cell(stats_table, 0, 3, "总交易次数", text_color=color.black, text_size=size.small)
    table.cell(stats_table, 1, 3, str.tostring(strategy.closedtrades), text_color=color.black, text_size=size.small)
    
    table.cell(stats_table, 0, 4, "胜率", text_color=color.black, text_size=size.small)
    win_rate = strategy.closedtrades > 0 ? (strategy.wintrades / strategy.closedtrades) * 100 : 0
    table.cell(stats_table, 1, 4, str.tostring(win_rate, "#.##") + "%", text_color=color.black, text_size=size.small)
    
    table.cell(stats_table, 0, 5, "净利润", text_color=color.black, text_size=size.small)
    net_profit_pct = strategy.initial_capital > 0 ? (strategy.netprofit / strategy.initial_capital) * 100 : 0
    table.cell(stats_table, 1, 5, str.tostring(net_profit_pct, "#.##") + "%", 
               text_color=strategy.netprofit >= 0 ? color.green : color.red, text_size=size.small)
    
    table.cell(stats_table, 0, 6, "最大回撤", text_color=color.black, text_size=size.small)
    max_dd = strategy.max_drawdown / strategy.initial_capital * 100
    table.cell(stats_table, 1, 6, str.tostring(max_dd, "#.##") + "%", text_color=color.red, text_size=size.small)
    
    table.cell(stats_table, 0, 7, "盈亏比", text_color=color.black, text_size=size.small)
    profit_factor = strategy.grossprofit > 0 and strategy.grossloss > 0 ? strategy.grossprofit / strategy.grossloss : 0
    table.cell(stats_table, 1, 7, str.tostring(profit_factor, "#.##"), text_color=color.black, text_size=size.small)
    
    table.cell(stats_table, 0, 8, "平均盈利", text_color=color.black, text_size=size.small)
    avg_win = strategy.wintrades > 0 ? strategy.grossprofit / strategy.wintrades : 0
    table.cell(stats_table, 1, 8, str.tostring(avg_win, "#.##"), text_color=color.green, text_size=size.small)
    
    table.cell(stats_table, 0, 9, "平均亏损", text_color=color.black, text_size=size.small)
    avg_loss = strategy.losstrades > 0 ? strategy.grossloss / strategy.losstrades : 0
    table.cell(stats_table, 1, 9, str.tostring(avg_loss, "#.##"), text_color=color.red, text_size=size.small)
    
    table.cell(stats_table, 0, 10, "连续亏损", text_color=color.black, text_size=size.small)
    table.cell(stats_table, 1, 10, str.tostring(consecutive_losses), 
               text_color=consecutive_losses > 2 ? color.red : color.black, text_size=size.small)
    
    table.cell(stats_table, 0, 11, "当前回撤", text_color=color.black, text_size=size.small)
    table.cell(stats_table, 1, 11, str.tostring(current_drawdown, "#.##") + "%", 
               text_color=current_drawdown > 10 ? color.red : color.black, text_size=size.small)

// 风险面板
if show_risk_panel and barstate.islast
    var table risk_table = table.new(position.top_left, 2, 8, bgcolor=color.white, border_width=1)
    
    table.cell(risk_table, 0, 0, "风险监控", text_color=color.white, text_size=size.normal, bgcolor=color.red)
    table.cell(risk_table, 1, 0, "", text_color=color.white, text_size=size.normal, bgcolor=color.red)
    
    table.cell(risk_table, 0, 1, "风险模式", text_color=color.black, text_size=size.small)
    table.cell(risk_table, 1, 1, risk_mode, text_color=color.black, text_size=size.small)
    
    table.cell(risk_table, 0, 2, "仓位模式", text_color=color.black, text_size=size.small)
    table.cell(risk_table, 1, 2, position_mode, text_color=color.black, text_size=size.small)
    
    table.cell(risk_table, 0, 3, "当前仓位", text_color=color.black, text_size=size.small)
    current_position_pct = strategy.position_size != 0 ? 
                           math.abs(strategy.position_size * close / strategy.equity) * 100 : 0
    table.cell(risk_table, 1, 3, str.tostring(current_position_pct, "#.##") + "%", text_color=color.black, text_size=size.small)
    
    table.cell(risk_table, 0, 4, "止损价格", text_color=color.black, text_size=size.small)
    sl_text = not na(current_sl) ? str.tostring(current_sl, "#.####") : "无"
    table.cell(risk_table, 1, 4, sl_text, text_color=color.red, text_size=size.small)
    
    table.cell(risk_table, 0, 5, "止盈价格", text_color=color.black, text_size=size.small)
    tp_text = not na(current_tp) ? str.tostring(current_tp, "#.####") : "无"
    table.cell(risk_table, 1, 5, tp_text, text_color=color.green, text_size=size.small)
    
    table.cell(risk_table, 0, 6, "加仓层数", text_color=color.black, text_size=size.small)
    table.cell(risk_table, 1, 6, str.tostring(pyramid_count), text_color=color.black, text_size=size.small)
    
    table.cell(risk_table, 0, 7, "风险状态", text_color=color.black, text_size=size.small)
    risk_status = risk_limits_ok ? "正常" : "警告"
    table.cell(risk_table, 1, 7, risk_status, text_color=risk_limits_ok ? color.green : color.red, text_size=size.small)

//              ╔════════════════════════════════╗              //
//              ║           警报系统             ║              //
//              ╚════════════════════════════════╝              //

alertcondition(long_condition, title="高级策略做多信号", 
     message="自适应趋势流高级策略做多信号 {{exchange}}:{{ticker}} 价格: {{close}} 趋势: 看涨")
alertcondition(short_condition, title="高级策略做空信号", 
     message="自适应趋势流高级策略做空信号 {{exchange}}:{{ticker}} 价格: {{close}} 趋势: 看跌")
alertcondition(current_drawdown > max_drawdown_pct * 0.8, title="回撤警告", 
     message="策略回撤接近限制 当前回撤: " + str.tostring(current_drawdown, "#.##") + "%")
alertcondition(consecutive_losses >= max_consecutive_loss - 1, title="连续亏损警告", 
     message="连续亏损接近限制 当前连续亏损: " + str.tostring(consecutive_losses))

//              ╔════════════════════════════════╗              //
//              ║           创建者               ║              //
//              ╚════════════════════════════════╝              //

// ██████╗ ██╗   ██╗ █████╗ ███╗   ██╗████████╗     █████╗ ██╗      ██████╗  ██████╗ 
//██╔═══██╗██║   ██║██╔══██╗████╗  ██║╚══██╔══╝    ██╔══██╗██║     ██╔════╝ ██╔═══██╗
//██║   ██║██║   ██║███████║██╔██╗ ██║   ██║       ███████║██║     ██║  ███╗██║   ██║
//██║▄▄ ██║██║   ██║██╔══██║██║╚██╗██║   ██║       ██╔══██║██║     ██║   ██║██║   ██║
//╚██████╔╝╚██████╔╝██║  ██║██║ ╚████║   ██║       ██║  ██║███████╗╚██████╔╝╚██████╔╝
// ╚══▀▀═╝  ╚═════╝ ╚═╝  ╚═╝╚═╝  ╚═══╝   ╚═╝       ╚═╝  ╚═╝╚══════╝ ╚═════╝  ╚═════╝