//==================================================================================================================================================================================================================//
//===========================𝒦𝑒𝒶𝓃𝓊_𝑅𝒾𝒯𝓏 Power Of Stocks - Bollinger Band & 5Ema Strategy - Indicator Code Start - Keanu_RiTz{===========================//
// This source code is subject to the terms of the Mozilla Public License 2.0 at https://mozilla.org/MPL/2.0/
// © Keanu_ritz
//@version=5
//==================================================================================================================================================================================================================//
strategy(title='Power of Stocks with RSI and ATR Dynamic SL updated via DeepSeek', overlay=true)
//==================================================================================================================================================================================================================//

Ecusl = input.bool(false, title="启用额外的止损显示", group='Power Of Stocks - BB5Ema 组合策略 - 𝒦𝑒𝒶𝓃𝓊_𝑅𝒾𝒯𝓏')
cusl = input.int(defval=5, title='设置止损点数低于最低价或高于最高价 - BB5Ema 组合策略', minval=1, maxval=100, group='Power Of Stocks - BB5Ema 组合策略 - 𝒦𝑒𝒶𝓃𝓊_𝑅𝒾𝒯𝓏')
CRiRe = input.int(defval=3, title='风险回报比 - BB5Ema 组合策略', minval=2, maxval=25, group='Power Of Stocks - BB5Ema 组合策略 - 𝒦𝑒𝒶𝓃𝓊_𝑅𝒾𝒯𝓏')
ShowCSell = input.bool(true, '显示卖出信号 - BB5Ema 组合策略', group='Power Of Stocks - BB5Ema 组合策略 - 𝒦𝑒𝒶𝓃𝓊_𝑅𝒾𝒯𝓏')
ShowCBuy = input.bool(false, '显示买入信号 - BB5Ema 组合策略', group='Power Of Stocks - BB5Ema 组合策略 - 𝒦𝑒𝒶𝓃𝓊_𝑅𝒾𝒯𝓏')
CBSWCon = input.bool(defval=false, title='带额外条件的买卖 - K线收盘价 - BB5Ema 组合策略', tooltip='额外条件 1：K线收盘价大于/小于前一根K线收盘价。这有时可以避免错误的动作，但使用此条件也可能错过大的动作，因为你会在K线收盘后才进入交易，而不是在突破高/低点时进入', group='Power Of Stocks - BB5Ema 组合策略 - 𝒦𝑒𝒶𝓃𝓊_𝑅𝒾𝒯𝓏')

Ebbusl = input.bool(false, title="启用额外的止损显示", group='Power Of Stocks 布林带策略 - 𝒦𝑒𝒶𝓃𝓊_𝑅𝒾𝒯𝓏')
bbusl = input.int(defval=5, title='设置止损点数低于最低价或高于最高价 - 布林带', minval=1, maxval=100, group='Power Of Stocks 布林带策略 - 𝒦𝑒𝒶𝓃𝓊_𝑅𝒾𝒯𝓏')
BRiRe = input.int(defval=4, title='风险回报比 - 布林带', minval=2, maxval=25, group='Power Of Stocks 布林带策略 - 𝒦𝑒𝒶𝓃𝓊_𝑅𝒾𝒯𝓏')
ShowBSell = input.bool(false, '显示卖出信号 - 布林带', group='Power Of Stocks 布林带策略 - 𝒦𝑒𝒶𝓃𝓊_𝑅𝒾𝒯𝓏')
ShowBBuy = input.bool(false, '显示买入信号 - 布林带', group='Power Of Stocks 布林带策略 - 𝒦𝑒𝒶𝓃𝓊_𝑅𝒾𝒯𝓏')
BBSWCon = input.bool(defval=false, title='带额外条件的买卖 - K线收盘价 - 布林带', tooltip='额外条件 1：K线收盘价大于/小于前一根K线收盘价。这有时可以避免错误的动作，但使用此条件也可能错过大的动作，因为你会在K线收盘后才进入交易，而不是在突破高/低点时进入', group='Power Of Stocks 布林带策略 - 𝒦𝑒𝒶𝓃𝓊_𝑅𝒾𝒯𝓏')

Eusl = input.bool(false, title="启用额外的止损显示", group='Power Of Stocks 5EMA 策略 - 𝒦𝑒𝒶𝓃𝓊_𝑅𝒾𝒯𝓏')
usl = input.int(defval=5, title='设置止损点数低于最低价或高于最高价 - 5EMA 策略', minval=1, maxval=100, group='Power Of Stocks 5EMA 策略 - 𝒦𝑒𝒶𝓃𝓊_𝑅𝒾𝒯𝓏')
RiRe = input.int(defval=3, title='风险回报比 - 5EMA 策略', minval=2, maxval=25, group='Power Of Stocks 5EMA 策略 - 𝒦𝑒𝒶𝓃𝓊_𝑅𝒾𝒯𝓏')
ShowSell = input.bool(false, '显示卖出信号 - 5EMA 策略', group='Power Of Stocks 5EMA 策略 - 𝒦𝑒𝒶𝓃𝓊_𝑅𝒾𝒯𝓏')
ShowBuy = input.bool(false, '显示买入信号 - 5EMA 策略', group='Power Of Stocks 5EMA 策略 - 𝒦𝑒𝒶𝓃𝓊_𝑅𝒾𝒯𝓏')
BSWCon = input.bool(defval=false, title='带额外条件的买卖 - K线收盘价 - 5EMA 策略', tooltip='额外条件 1：K线收盘价大于/小于前一根K线收盘价。这有时可以避免错误的动作，但使用此条件也可能错过大的动作，因为你会在K线收盘后才进入交易，而不是在突破高/低点时进入', group='Power Of Stocks 5EMA 策略 - 𝒦𝑒𝒶𝓃𝓊_𝑅𝒾𝒯𝓏')

length = 20
src = close
mult = input.float(1.5, minval=0.001, maxval=50, title="布林带标准差", group='Power Of Stocks - BB5Ema 组合策略 - 𝒦𝑒𝒶𝓃𝓊_𝑅𝒾𝒯𝓏')
basis = ta.sma(src, length)
dev = mult * ta.stdev(src, length)
upper = basis + dev
lower = basis - dev
offset = 0
plot(ShowCSell or ShowCBuy or ShowBSell or ShowBBuy ? basis : na, "布林带基线", color=color.new(#FFFFFF,35),linewidth=4, offset = offset,display=display.all)
p1 = plot(ShowCSell or ShowCBuy or ShowBSell or ShowBBuy ? upper : na, "上布林带", color=color.new(#929594,50), offset = offset,display=display.all)
p2 = plot(ShowCSell or ShowCBuy or ShowBSell or ShowBBuy ? lower : na, "下布林带", color=color.new(#929594,50), offset = offset,display=display.all)
fill(p1, p2, title = "背景", color=color.new(#929594, 91))
ema5 = ta.ema(close,5)
pema5 = plot(ShowCSell or ShowCBuy or ShowSell or ShowBuy ? ema5 : na, '5 EMA', color=color.new(#882626, 0), linewidth=2)
rsi_period = input.int(14, title="RSI 周期", group="增强功能")
rsi_overbought = input.int(70, title="RSI 超买水平", group="增强功能")
rsi_oversold = input.int(40, title="RSI 超卖水平", group="增强功能")

atr_period = input.int(14, title="ATR 周期", group="增强功能")
atr_multiplier = input.float(1.5, title="用于止损的ATR倍数", group="增强功能")
enable_trailing_stop = input.bool(false, title="启用追踪止损", group="增强功能")
trailing_distance = input.float(1.5, title="追踪距离（ATR倍数）", group="增强功能")

// 计算RSI和ATR
rsi = ta.rsi(close, rsi_period)
atr = ta.atr(atr_period)
// 回测指标变量
var int total_trades = 0
var int winning_trades = 0
var int losing_trades = 0
var float gross_profit = 0.0
var float gross_loss = 0.0
var float max_drawdown = 0.0
var float peak_equity = strategy.equity
var float current_drawdown = 0.0
var float net_profit = 0.0
var label buy_alert_label = na
var label sell_alert_label = na

var bool CShort  = na
var bool CLong  = na
var int cshortC = 0
var int csslhitC = 0
var int cstarhitC = 0
var float cssl = na
var float cstarl = na
var float cstar = na
var float csellat = na
var float calert_shorthigh = na
var float calert_shortlow = na
var bool ccan_drawssl = false
var bool ccan_drawstar = false
var line clssl = na
var line clstar = na
var line clsell = na
var label clssllbl = na
var label clstarlbl = na
var label clselllbl = na
var int clongC = 0
var int clslhitC = 0
var int cltarhitC = 0
var float clsl = na
var float cltarl = na
var float cltar = na
var float cbuyat = na
var float calert_longhigh = na
var float calert_longlow = na
var bool ccan_drawlsl = false
var bool ccan_drawltar = false
var line cllsl = na
var line clltar = na
var line clbuy = na
var label cllsllbl = na
var label clltarlbl = na
var label clbuylbl = na

var bool UBBTrap  = na
var bool LBBTrap  = na
var int UBBTrapC = 0
var int bbsslhitC = 0
var int bbstarhitC = 0
var float bbssl = na
var float bbstarl = na
var float bbstar = na
var float bbsellat = na
var float bbalert_shorthigh = na
var float bbalert_shortlow = na
var line bblssl = na
var line bblstar = na
var line bblsell = na
var label bblssllbl = na
var label bblstarlbl = na
var label bblselllbl = na
var int LBBTrapC = 0
var int bblslhitC = 0
var int bbltarhitC = 0
var float bblsl = na
var float bbltarl = na
var float bbltar = na
var float bbbuyat = na
var float bbalert_longhigh = na
var float bbalert_longlow = na
var line bbllsl = na
var line bblltar = na
var line bblbuy = na
var label bbllsllbl = na
var label bblltarlbl = na
var label bblbuylbl = na
var bool Short  = na
var bool Long  = na
var int shortC = 0
var int sslhitC = 0
var int starhitC = 0
var float ssl = na
var float starl = na
var float star = na
var float sellat = na
var float alert_shorthigh = na
var float alert_shortlow = na
var line lssl = na
var line lstar = na
var line lsell = na
var label lssllbl = na
var label lstarlbl = na
var label lselllbl = na
var int longC = 0
var int lslhitC = 0
var int ltarhitC = 0
var float lsl = na
var float ltarl = na
var float ltar = na
var float buyat = na
var float alert_longhigh = na
var float alert_longlow = na
var line llsl = na
var line lltar = na
var line lbuy = na
var label llsllbl = na
var label lltarlbl = na
var label lbuylbl = na

CShortWC = low[1] > upper[1] and low[1] > ema5[1] and low < low[1] and cshortC == 0 and close < close[1] and rsi > rsi_overbought
CShortWOC = low[1] > upper[1] and low[1] > ema5[1] and low < low[1] and cshortC == 0 and rsi > rsi_overbought
CShort := CBSWCon ? CShortWC : CShortWOC
csslhit = high > cssl and cshortC > 0 and csslhitC == 0
cstarhit = low < cstar and cshortC > 0 and cstarhitC == 0

CLongWC = high[1] < lower[1] and high[1] < ema5[1] and high > high[1] and clongC == 0 and close > close[1] and rsi < rsi_oversold
CLongWOC = high[1] < lower[1] and high[1] < ema5[1] and high > high[1] and clongC == 0 and rsi < rsi_oversold
CLong := CBSWCon ? CLongWC : CLongWOC
clslhit = low < clsl and clongC > 0 and clslhitC == 0
cltarhit = high > cltar and clongC > 0 and cltarhitC == 0

UBBTrapWC = low[1] > upper[1] and low[1] > low and UBBTrapC == 0 and close<close[1]
UBBTrapWOC = low[1] > upper[1] and low[1] > low and UBBTrapC == 0
UBBTrap := BBSWCon ? UBBTrapWC : UBBTrapWOC
bbsslhit = high > bbssl and UBBTrapC > 0 and bbsslhitC == 0
bbstarhit = low < bbstar and UBBTrapC > 0 and bbstarhitC == 0

LBBTrapWC =  high[1] < lower[1] and high[1] < high and LBBTrapC == 0 and close>close[1]
LBBTrapWOC =  high[1] < lower[1] and high[1] < high and LBBTrapC == 0
LBBTrap := BBSWCon ? LBBTrapWC : LBBTrapWOC
bblslhit = low < bblsl and LBBTrapC > 0 and bblslhitC == 0
bbltarhit = high > bbltar and LBBTrapC > 0 and bbltarhitC == 0

ShortWC = low[1] > ema5[1] and low[1] > low and shortC == 0 and close<close[1]
ShortWOC = low[1] > ema5[1] and low[1] > low and shortC == 0
Short := BSWCon ? ShortWC : ShortWOC
sslhit = high > ssl and shortC > 0 and sslhitC == 0
starhit = low < star and shortC > 0 and starhitC == 0

LongWC =  high[1] < ema5[1] and high[1] < high and longC == 0 and close>close[1]
LongWOC =  high[1] < ema5[1] and high[1] < high and longC == 0
Long := BSWCon ? LongWC : LongWOC
lslhit = low < lsl and longC > 0 and lslhitC == 0
ltarhit = high > ltar and longC > 0 and ltarhitC == 0
// ========================== 将预警K线颜色改为黄色 ==========================
// ========================== 将预警K线颜色改为黄色 ==========================
// 组合策略潜在买入条件
prevBuyCondition = high < lower and high < ema5 // 上一根K线完全低于下布林带和5EMA
prevSellCondition = low > upper and low > ema5 // 上一根K线完全高于上布林带和5EMA

volFilter = volume > ta.sma(volume, 20) // 检查当前成交量是否高于20周期平均值

htfEma50 = request.security(syminfo.tickerid, "60", ta.ema(close, 50))

// 更新的买入/卖出条件，加入更高时间框架的趋势检查
buyCombinedSetup = prevBuyCondition and close > high[1] and volFilter and close > htfEma50
sellCombinedSetup = prevSellCondition and close < low[1] and volFilter and close < htfEma50


// 将上一根K线的颜色改为黄色
barcolor(prevBuyCondition ? color.yellow : na)
barcolor(prevSellCondition ? color.yellow : na)
if CShort and ShowCSell
    cshortC := cshortC + 1
    csslhitC := 0
    cstarhitC := 0
    calert_shorthigh := high[1]
    cssl := high[1] + atr * atr_multiplier // 使用ATR动态止损
    cstarl := CBSWCon ? ((high[1] - close) + atr * atr_multiplier) * CRiRe : ((high[1] - low[1]) + atr * atr_multiplier) * CRiRe
    cstar := CBSWCon ? close - cstarl : low[1] - cstarl
    csellat := CBSWCon ? close : low[1]
    ccan_drawssl := true
    ccan_drawstar := true
    clssl := line.new(bar_index, cssl, bar_index, cssl, color=color.new(#fc2d01,25), style=line.style_dashed)
    clstar := line.new(bar_index, cstar, bar_index, cstar, color=color.new(color.green,25), style=line.style_dashed)
    clsell := line.new(bar_index, csellat, bar_index, csellat, color=color.new(color.orange,25), style=line.style_dashed)
    // clssllbl := label.new(bar_index, cssl, style=label.style_none, text='Short SL - Combined' + ' (' + str.tostring(cssl) + ')', textcolor=color.new(#fc2d01,35), color=color.new(#fc2d01,35))
    // clstarlbl := label.new(bar_index, cstar, style=label.style_none, text='Short Target - Combined' + ' (' + str.tostring(cstar) + ')', textcolor=color.new(color.green,35), color=color.new(color.green,35))
    // clselllbl := label.new(bar_index, csellat, style=label.style_none, text='Sell at - Combined' + ' (' + str.tostring(csellat) + ')', textcolor=color.new(color.orange,35), color=color.new(color.orange,35))
    // 初始化追踪止损


if csslhit == false and cstarhit == false and cshortC > 0
    line.set_x2(clssl, bar_index)
    line.set_x2(clstar, bar_index)
    line.set_x2(clsell, bar_index)
    csslhitC := 0
    cstarhitC := 0
else
    if csslhit
        ccan_drawssl := false
        cshortC := 0
        csslhitC := csslhitC + 1
    else 
        if cstarhit
            ccan_drawstar := false
            cshortC := 0
            cstarhitC := cstarhitC + 1  

if CLong and ShowCBuy
    clongC := clongC + 1
    clslhitC := 0
    cltarhitC := 0
    calert_longlow := low[1]
    clsl := low[1] - atr * atr_multiplier // 使用ATR动态止损
    cltarl := CBSWCon ? ((close - low[1]) + atr * atr_multiplier) * CRiRe : ((high[1] - low[1]) + atr * atr_multiplier) * CRiRe
    cltar := CBSWCon ? close + cltarl : high[1] + cltarl
    cbuyat := CBSWCon ? close : high[1]
    ccan_drawlsl := true
    ccan_drawltar := true
    cllsl := line.new(bar_index, clsl, bar_index, clsl, color=color.new(#fc2d01,25), style=line.style_dotted)
    clltar := line.new(bar_index, cltar, bar_index, cltar, color=color.new(color.green,25), style=line.style_dotted)
    clbuy := line.new(bar_index, cbuyat, bar_index, cbuyat, color=color.new(color.orange,25), style=line.style_dotted)
    // cllsllbl := label.new(bar_index, clsl, style=label.style_none, text='Long SL - Combined' + ' (' + str.tostring(clsl) + ')', textcolor=color.new(#fc2d01,35), color=color.new(#fc2d01,35))
    // clltarlbl := label.new(bar_index, cltar, style=label.style_none, text='Long Target - Combined' + ' (' + str.tostring(cltar) + ')', textcolor=color.new(color.green,35), color=color.new(color.green,35))
    // clbuylbl := label.new(bar_index, cbuyat, style=label.style_none, text='Buy at - Combined' + ' (' + str.tostring(cbuyat) + ')', textcolor=color.new(color.orange,35), color=color.new(color.orange,35))
    // 初始化追踪止损
  


if clslhit == false and cltarhit == false and clongC > 0
    line.set_x2(cllsl, bar_index)
    line.set_x2(clltar, bar_index)
    line.set_x2(clbuy, bar_index)
    clslhitC := 0
    cltarhitC := 0
else
    if clslhit
        ccan_drawlsl := false
        clongC := 0
        clslhitC := clslhitC + 1
    else
        if cltarhit
            ccan_drawltar := false
            clongC := 0
            cltarhitC := cltarhitC + 1


if UBBTrap and ShowBSell
    UBBTrapC := UBBTrapC + 1
    bbsslhitC := 0
    bbstarhitC := 0
    bbalert_shorthigh := high[1]
    if Ebbusl
        bbssl := high[1] + bbusl
        bbstarl := BBSWCon ? ((high[1] - close) + bbusl) * BRiRe : ((high[1] - low[1]) + bbusl) * BRiRe
    else
        bbssl := high[1]
        bbstarl := BBSWCon ? (high[1] - close) * BRiRe : (high[1] - low[1]) * BRiRe
    bbstar := BBSWCon ? close - bbstarl : low[1] - bbstarl
    bbsellat := BBSWCon ? close : low[1]
    bblssl := line.new(bar_index, bbssl, bar_index, bbssl, color=color.new(#fc2d01,45), style=line.style_dashed)
    bblstar := line.new(bar_index, bbstar, bar_index, bbstar, color=color.new(color.green,45), style=line.style_dashed)
    bblsell := line.new(bar_index, bbsellat, bar_index, bbsellat, color=color.new(color.orange,45), style=line.style_dashed)
    bblssllbl := label.new(bar_index, bbssl, style=label.style_none, text='Short SL - BB' + ' (' + str.tostring(bbssl) + ')', textcolor=color.new(#fc2d01,35), color=color.new(#fc2d01,35))
    bblstarlbl := label.new(bar_index, bbstar, style=label.style_none, text='Short Target - BB' + ' (' + str.tostring(bbstar) + ')', textcolor=color.new(color.green,35), color=color.new(color.green,35))
    bblselllbl := label.new(bar_index, bbsellat, style=label.style_none, text='Sell at - BB' + ' (' + str.tostring(bbsellat) + ')', textcolor=color.new(color.orange,35), color=color.new(color.orange,35))
  
if bbsslhit == false and bbstarhit == false and UBBTrapC > 0
    line.set_x2(bblssl, bar_index)
    line.set_x2(bblstar, bar_index)
    line.set_x2(bblsell, bar_index)
    bbsslhitC := 0
    bbstarhitC := 0
else
    if bbsslhit
        UBBTrapC := 0
        bbsslhitC := bbsslhitC + 1
    else
        if bbstarhit
            UBBTrapC := 0
            bbstarhitC := bbstarhitC + 1      

if LBBTrap and ShowBBuy
    LBBTrapC := LBBTrapC + 1
    bblslhitC := 0
    bbltarhitC := 0
    bbalert_longlow := low[1]
    if Ebbusl
        bblsl := low[1] - bbusl
        bbltarl := BBSWCon ? ((close - low[1]) + bbusl) * BRiRe : ((high[1] - low[1]) + bbusl) * BRiRe
    else
        bblsl := low[1]
        bbltarl := BBSWCon ? (close - low[1]) * BRiRe : (high[1] - low[1]) * BRiRe
    bbltar := BBSWCon ? close + bbltarl : high[1] + bbltarl
    bbbuyat := BBSWCon ? close : high[1]
    bbllsl := line.new(bar_index, bblsl, bar_index, bblsl, color=color.new(#fc2d01,45), style=line.style_dotted)
    bblltar := line.new(bar_index, bbltar, bar_index, bbltar, color=color.new(color.green,45), style=line.style_dotted)
    bblbuy := line.new(bar_index, bbbuyat, bar_index, bbbuyat, color=color.new(color.orange,45), style=line.style_dotted)
    bbllsllbl := label.new(bar_index, bblsl, style=label.style_none, text='Long SL - BB' + ' (' + str.tostring(bblsl) + ')', textcolor=color.new(#fc2d01,35), color=color.new(#fc2d01,35))
    bblltarlbl := label.new(bar_index, bbltar, style=label.style_none, text='Long Target - BB' + ' (' + str.tostring(bbltar) + ')', textcolor=color.new(color.green,35), color=color.new(color.green,35))
    bblbuylbl := label.new(bar_index, bbbuyat, style=label.style_none, text='Buy at - BB' + ' (' + str.tostring(bbbuyat) + ')', textcolor=color.new(color.orange,35), color=color.new(color.orange,35))

if bblslhit == false and bbltarhit == false and LBBTrapC > 0
    line.set_x2(bbllsl, bar_index)
    line.set_x2(bblltar, bar_index)
    line.set_x2(bblbuy, bar_index)
    bblslhitC := 0
    bbltarhitC := 0
else
    if bblslhit
        LBBTrapC := 0
        bblslhitC := bblslhitC + 1
    else
        if bbltarhit
            LBBTrapC := 0
            bbltarhitC := bbltarhitC + 1

if Short and ShowSell
    shortC := shortC + 1
    sslhitC := 0
    starhitC := 0
    alert_shorthigh := high[1]
    if Eusl
        ssl := high[1] + usl
        starl := BSWCon ? ((high[1] - close) + usl) * RiRe : ((high[1] - low[1]) + usl) * RiRe
    else
        ssl := high[1]
        starl := BSWCon ? (high[1] - close) * RiRe : (high[1] - low[1]) * RiRe
    star := BSWCon ? close - starl : low[1] - starl
    sellat := BSWCon ? close : low[1]
    lssl := line.new(bar_index, ssl, bar_index, ssl, color=color.new(#fc2d01,45), style=line.style_dashed)
    lstar := line.new(bar_index, star, bar_index, star, color=color.new(color.green,45), style=line.style_dashed)
    lsell := line.new(bar_index, sellat, bar_index, sellat, color=color.new(color.orange,45), style=line.style_dashed)
    lssllbl := label.new(bar_index, ssl, style=label.style_none, text='Short SL - 5ema' + ' (' + str.tostring(ssl) + ')', textcolor=color.new(#fc2d01,35), color=color.new(#fc2d01,35))
    lstarlbl := label.new(bar_index, star, style=label.style_none, text='Short Target - 5ema' + ' (' + str.tostring(star) + ')', textcolor=color.new(color.green,35), color=color.new(color.green,35))
    lselllbl := label.new(bar_index, sellat, style=label.style_none, text='Sell at - 5ema' + ' (' + str.tostring(sellat) + ')', textcolor=color.new(color.orange,35), color=color.new(color.orange,35))

if sslhit == false and starhit == false and shortC > 0
    line.set_x2(lssl, bar_index)
    line.set_x2(lstar, bar_index)
    line.set_x2(lsell, bar_index)
    sslhitC := 0
    starhitC := 0
else
    if sslhit
        shortC := 0
        sslhitC := sslhitC + 1
    else
        if low < star and shortC > 0 and starhitC == 0
            shortC := 0
            starhitC := starhitC + 1

if Long and ShowBuy
    longC := longC + 1
    lslhitC := 0
    ltarhitC := 0
    alert_longlow := low[1]
    if Eusl
        lsl := low[1] - usl
        ltarl := BSWCon ? ((close - low[1]) + usl) * RiRe : ((high[1] - low[1]) + usl) * RiRe
    else
        lsl := low[1]
        ltarl := BSWCon ? (close - low[1]) * RiRe : (high[1] - low[1]) * RiRe
    ltar := BSWCon ? close + ltarl : high[1] + ltarl
    buyat := BSWCon ? close : high[1]
    llsl := line.new(bar_index, lsl, bar_index, lsl, color=color.new(#fc2d01,45), style=line.style_dotted)
    lltar := line.new(bar_index, ltar, bar_index, ltar, color=color.new(color.green,45), style=line.style_dotted)
    lbuy := line.new(bar_index, buyat, bar_index, buyat, color=color.new(color.orange,45), style=line.style_dotted)
    llsllbl := label.new(bar_index, lsl, style=label.style_none, text='Long SL - 5ema' + ' (' + str.tostring(lsl) + ')', textcolor=color.new(#fc2d01,35), color=color.new(#fc2d01,35))
    lltarlbl := label.new(bar_index, ltar, style=label.style_none, text='Long Target - 5ema' + ' (' + str.tostring(ltar) + ')', textcolor=color.new(color.green,35), color=color.new(color.green,35))
    lbuylbl := label.new(bar_index, buyat, style=label.style_none, text='Buy at - 5ema' + ' (' + str.tostring(buyat) + ')', textcolor=color.new(color.orange,35), color=color.new(color.orange,35))

if lslhit == false and ltarhit == false and longC > 0
    line.set_x2(llsl, bar_index)
    line.set_x2(lltar, bar_index)
    line.set_x2(lbuy, bar_index)
    lslhitC := 0
    ltarhitC := 0  
else
    if lslhit
        longC := 0
        lslhitC := lslhitC + 1
    else
        if ltarhit
            longC := 0
            ltarhitC := ltarhitC + 1


plotshape(ShowCSell and CShort, title='卖出 - 组合策略', location=location.abovebar, offset=0, color=color.new(#e74c3c, 65), style=shape.arrowdown, size=size.normal, text='卖出 - 组合', textcolor=color.new(#e74c3c, 55))
plotshape(ShowCSell and csslhit, title='短线止损触发 - 组合策略', location=location.abovebar, offset=0, color=color.new(#fc2d01, 65), style=shape.arrowdown, size=size.normal, text='短线止损触发 - 组合', textcolor=color.new(#fc2d01, 25))
plotshape(ShowCSell and cstarhit, title='短线目标触发 - 组合策略', location=location.belowbar, offset=0, color=color.new(color.green, 65), style=shape.arrowup, size=size.normal, text='短线目标触发 - 组合', textcolor=color.new(color.green, 55))
plotshape(ShowCBuy and CLong, title='买入 - 组合策略', location=location.belowbar, offset=0, color=color.new(#2ecc71, 65), style=shape.arrowup, size=size.normal, text='买入 - 组合', textcolor=color.new(#2ecc71, 55))
plotshape(ShowCBuy and clslhit, title='长线止损触发 - 组合策略', location=location.belowbar, offset=0, color=color.new(#fc2d01, 65), style=shape.arrowdown, size=size.normal, text='长线止损触发 - 组合', textcolor=color.new(#fc2d01, 25))
plotshape(ShowCBuy and cltarhit, title='长线目标触发 - 组合策略', location=location.abovebar, offset=0, color=color.new(color.green, 65), style=shape.arrowup, size=size.normal, text='长线目标触发 - 组合', textcolor=color.new(color.green, 55))
plotshape(ShowBSell and UBBTrap, title='卖出 - 布林带策略', location=location.abovebar, offset=0, color=color.new(#e74c3c, 45), style=shape.arrowdown, size=size.normal, text='卖出 - 布林带', textcolor=color.new(#e74c3c, 55))
plotshape(ShowBSell and bbsslhit, title='短线止损触发 - 布林带策略', location=location.abovebar, offset=0, color=color.new(#fc2d01, 25), style=shape.arrowdown, size=size.normal, text='短线止损触发 - 布林带', textcolor=color.new(#fc2d01, 25))
plotshape(ShowBSell and bbstarhit, title='短线目标触发 - 布林带策略', location=location.belowbar, offset=0, color=color.new(color.green, 45), style=shape.arrowup, size=size.normal, text='短线目标触发 - 布林带', textcolor=color.new(color.green, 55))
plotshape(ShowBBuy and LBBTrap, title='买入 - 布林带策略', location=location.belowbar, offset=0, color=color.new(#2ecc71, 45), style=shape.arrowup, size=size.normal, text='买入 - 布林带', textcolor=color.new(#2ecc71, 55))
plotshape(ShowBBuy and bblslhit, title='长线止损触发 - 布林带策略', location=location.belowbar, offset=0, color=color.new(#fc2d01, 25), style=shape.arrowdown, size=size.normal, text='长线止损触发 - 布林带', textcolor=color.new(#fc2d01, 25))
plotshape(ShowBBuy and bbltarhit, title='长线目标触发 - 布林带策略', location=location.abovebar, offset=0, color=color.new(color.green, 45), style=shape.arrowup, size=size.normal, text='长线目标触发 - 布林带', textcolor=color.new(color.green, 55))

plotshape(ShowSell and Short, title='卖出 - 5EMA 策略', location=location.abovebar, offset=0, color=color.new(#e74c3c, 45), style=shape.arrowdown, size=size.normal, text='卖出 - 5EMA', textcolor=color.new(#e74c3c, 55))
plotshape(ShowSell and sslhit, title='短线止损触发 - 5EMA 策略', location=location.abovebar, offset=0, color=color.new(#fc2d01, 25), style=shape.arrowdown, size=size.normal, text='短线止损触发 - 5EMA', textcolor=color.new(#fc2d01, 25))
plotshape(ShowSell and starhit, title='短线目标触发 - 5EMA 策略', location=location.belowbar, offset=0, color=color.new(color.green, 45), style=shape.arrowup, size=size.normal, text='短线目标触发 - 5EMA 策略', textcolor=color.new(color.green, 55))
plotshape(ShowBuy and Long, title='买入 - 5EMA 策略', location=location.belowbar, offset=0, color=color.new(#2ecc71, 45), style=shape.arrowup, size=size.normal, text='买入 - 5EMA', textcolor=color.new(#2ecc71, 55))
plotshape(ShowBuy and lslhit, title='长线止损触发 - 5EMA 策略', location=location.belowbar, offset=0, color=color.new(#fc2d01, 25), style=shape.arrowdown, size=size.normal, text='长线止损触发 - 5EMA', textcolor=color.new(#fc2d01, 25))
plotshape(ShowBuy and ltarhit, title='长线目标触发 - 5EMA 策略', location=location.abovebar, offset=0, color=color.new(color.green, 45), style=shape.arrowup, size=size.normal, text='长线目标触发 - 5EMA', textcolor=color.new(color.green, 55))

if ShowCSell and CShort
    alert("做空@ " + str.tostring(csellat) + " : 止损@ " + str.tostring(cssl) + " : 目标@ " + str.tostring(cstar) + " - 组合策略", alert.freq_once_per_bar )

if ShowCSell and csslhit
    alert("组合策略短线止损触发", alert.freq_once_per_bar )

if ShowCSell and cstarhit
    alert("组合策略短线目标触发", alert.freq_once_per_bar )

if ShowCBuy and CLong
    alert("做多@ " + str.tostring(cbuyat) + " : 止损@ " + str.tostring(clsl) + " : 目标@ " + str.tostring(cltar) + " - 组合策略", alert.freq_once_per_bar )

if ShowCBuy and clslhit
    alert("组合策略长线止损触发", alert.freq_once_per_bar )

if ShowCBuy and cltarhit
    alert("组合策略长线目标触发", alert.freq_once_per_bar )

if ShowBSell and UBBTrap
    alert("做空@ " + str.tostring(bbsellat) + " : 止损@ " + str.tostring(bbssl) + " : 目标@ " + str.tostring(bbstar) + " - 布林带策略", alert.freq_once_per_bar)

if ShowBSell and bbsslhit
    alert("布林带策略短线止损触发", alert.freq_once_per_bar)

if ShowBSell and bbstarhit
    alert("布林带策略短线目标触发", alert.freq_once_per_bar)

if ShowBBuy and LBBTrap
    alert("做多@ " + str.tostring(bbbuyat) + " : 止损@ " + str.tostring(bblsl) + " : 目标@ " + str.tostring(bbltar) + " - 布林带策略", alert.freq_once_per_bar)

if ShowBBuy and bblslhit
    alert("布林带策略长线止损触发", alert.freq_once_per_bar)

if ShowBBuy and bbltarhit
    alert("布林带策略长线目标触发", alert.freq_once_per_bar)

if ShowSell and Short
    alert("做空@ " + str.tostring(sellat) + " : 止损@ " + str.tostring(ssl) + " : 目标@ " + str.tostring(star) + " - 5EMA 策略", alert.freq_once_per_bar)

if ShowSell and sslhit
    alert("5EMA 策略短线止损触发", alert.freq_once_per_bar)

if ShowSell and starhit
    alert("5EMA 策略短线目标触发", alert.freq_once_per_bar)

if ShowBuy and Long
    alert("做多@ " + str.tostring(buyat) + " : 止损@ " + str.tostring(lsl) + " : 目标@ " + str.tostring(ltar) + " - 5EMA 策略", alert.freq_once_per_bar)

if ShowBuy and lslhit
    alert("5EMA 策略长线止损触发", alert.freq_once_per_bar)

if ShowBuy and ltarhit
    alert("5EMA 策略长线目标触发", alert.freq_once_per_bar)

//===========================𝒦𝑒𝒶𝓃𝓊_𝑅𝒾𝒯𝓏 Power Of Stocks - Bollinger Band & 5Ema Strategy - Indicator Code End - Keanu_RiTz===========================]//

//==================================================================================================================================================================================================================//


if clslhit or csslhit
    // 止损触发
    losing_trades := losing_trades + 1
    gross_loss := gross_loss + math.abs(clsl - cbuyat) // 对于多单
    gross_loss := gross_loss + math.abs(cssl - csellat) // 对于空单

if cltarhit or cstarhit
    // 目标触发
    winning_trades := winning_trades + 1
    gross_profit := gross_profit + math.abs(cltar - cbuyat) // 对于多单
    gross_profit := gross_profit + math.abs(cstar - csellat) // 对于空单

// 更新峰值权益
if strategy.equity > peak_equity
    peak_equity := strategy.equity

// 计算当前回撤
current_drawdown := peak_equity - strategy.equity
if current_drawdown > max_drawdown
    max_drawdown := current_drawdown

if CLong or CShort
    total_trades := total_trades + 1

// 计算胜率
win_rate = total_trades > 0 ? (winning_trades / total_trades) * 100 : 0

// 计算风险回报比
average_reward = winning_trades > 0 ? gross_profit / winning_trades : 0
average_risk = losing_trades > 0 ? gross_loss / losing_trades : 0
risk_to_reward_ratio = average_risk > 0 ? average_reward / average_risk : 0

// 计算盈利因子
profit_factor = gross_loss > 0 ? gross_profit / gross_loss : 0

// 计算净收益
net_profit := gross_profit - gross_loss

// 创建一个指标表格
var table metrics_table = table.new(position.top_right, 2, 7, bgcolor=color.new(color.black, 70))

// 填充表格
table.cell(metrics_table, 0, 0, "总交易次数", text_color=color.white)
table.cell(metrics_table, 1, 0, str.tostring(total_trades), text_color=color.white)

table.cell(metrics_table, 0, 1, "胜率 (%)", text_color=color.white)
table.cell(metrics_table, 1, 1, str.tostring(win_rate, "#.##"), text_color=color.green)

table.cell(metrics_table, 0, 2, "风险回报比", text_color=color.white)
table.cell(metrics_table, 1, 2, str.tostring(risk_to_reward_ratio, "#.##"), text_color=color.white)

table.cell(metrics_table, 0, 3, "盈利因子", text_color=color.white)
table.cell(metrics_table, 1, 3, str.tostring(profit_factor, "#.##"), text_color=color.white)

table.cell(metrics_table, 0, 4, "最大回撤", text_color=color.white)
table.cell(metrics_table, 1, 4, str.tostring(max_drawdown, "#.##"), text_color=color.red)

table.cell(metrics_table, 0, 5, "毛利润", text_color=color.white)
table.cell(metrics_table, 1, 5, str.tostring(gross_profit, "#.##"), text_color=color.green)

table.cell(metrics_table, 0, 6, "净收益", text_color=color.white)
table.cell(metrics_table, 1, 6, str.tostring(net_profit, "#.##"), text_color=net_profit >= 0 ? color.green : color.red)

// 用于跟踪赢和输的变量
var int win_trades = 0
var int loss_trades = 0

// 检查多单
if (not na(clsl) and not clslhit)
    // 如果止损未被触发，计为赢
    win_trades := win_trades + 1
else if (clslhit)
    // 如果止损被触发，计为输
    loss_trades := loss_trades + 1

// 检查空单
if (not na(cssl) and not csslhit)
    // 如果止损未被触发，计为赢
    win_trades := win_trades + 1
else if (csslhit)
    // 如果止损被触发，计为输
    loss_trades := loss_trades + 1

// 计算新的胜率
total_trades_new = win_trades + loss_trades
win_rate_new = total_trades_new > 0 ? (win_trades / total_trades_new) * 100 : 0

// 更新指标表格中的新胜率
table.cell(metrics_table, 0, 1, "新胜率 (%)", text_color=color.white)
table.cell(metrics_table, 1, 1, str.tostring(win_rate_new, "#.##"), text_color=color.green)

