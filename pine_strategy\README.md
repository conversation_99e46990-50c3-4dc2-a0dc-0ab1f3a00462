# 缠论交易指标与策略

这个项目包含基于缠论理论的TradingView Pine Script指标和交易策略。缠论是由"缠中说禅"提出的一种技术分析方法，通过识别K线形态、画笔和中枢来分析市场结构和趋势变化。

## 文件说明

项目包含两个主要文件：

1. `chan_theory_indicator.pine` - 纯指标版本，仅用于显示分型、笔和中枢
2. `chan_theory_strategy.pine` - 策略版本，包含完整的交易逻辑和回测功能

## 缠论基本概念

- **分型**：由顶分型和底分型组成，是缠论分析的基础单位
- **笔**：由一个顶分型和一个底分型连接形成，代表一个完整的价格波动
- **中枢**：由三段以上的重叠区间构成，是价格波动的聚集区域

## 如何使用

### 在TradingView中导入脚本

1. 登录TradingView账户
2. 点击"Pine编辑器"按钮
3. 点击"打开"，然后选择"从文件导入"
4. 选择下载的 `.pine`文件

### 指标参数说明

- **分型长度**：设置识别分型的K线数量，默认为2
- **显示分型**：控制是否显示顶底分型标记
- **笔宽度**：调整笔线的粗细
- **显示笔**：控制是否显示笔
- **高亮中枢**：控制是否用紫色方框高亮显示中枢区域

### 策略参数说明

除了上述指标参数外，策略版本还包含以下交易参数：

- **启用交易信号**：控制是否执行交易
- **止盈百分比**：设置止盈点位，以入场价格的百分比表示
- **止损百分比**：设置止损点位，以入场价格的百分比表示
- **交易确认K线数**：设置确认交易信号所需的连续K线数量

## 交易逻辑

该策略基于以下交易规则：

1. **做多信号**：价格向上突破中枢上边界时
2. **做空信号**：价格向下突破中枢下边界时
3. **平多信号**：多头持仓时价格回调至中枢区域
4. **平空信号**：空头持仓时价格回调至中枢区域
5. **止盈止损**：根据设置的百分比自动执行

## 注意事项

- 缠论分析适用于各种时间周期，但建议在较长时间周期（如日线、4小时线）上使用效果更佳
- 该策略仅供学习研究使用，实盘交易请谨慎
- 建议先使用指标版本熟悉缠论概念，再尝试策略版本进行回测

## 进一步优化方向

1. 增加对笔和段更精确的判断
2. 完善中枢识别算法
3. 加入更多缠论专有概念，如线段、趋势通道等
4. 增加多种交易策略选择

## 参考资料

- 《教你炒股票》系列文章 - 缠中说禅
- 《缠论一筋》
- 《缠论108课》
