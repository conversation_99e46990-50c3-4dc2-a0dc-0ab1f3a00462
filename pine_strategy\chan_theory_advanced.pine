// This source code is subject to the terms of the Mozilla Public License 2.0 at https://mozilla.org/MPL/2.0/
// ©jdehorty

// @version=5
indicator('机器学习: 洛伦兹分类', '洛伦兹分类', true, precision=4, max_labels_count=500) 

import jdehorty/MLExtensions/2 as ml
import jdehorty/KernelFunctions/2 as kernels

// ====================
// ==== 背景介绍 ====
// ====================

// 在使用K最近邻等机器学习算法时，选择合适的距离度量至关重要。欧几里得距离通常
// 被用作默认的距离度量，但它可能并不总是最佳选择。这是因为市场数据通常受到重大
// 世界事件（如FOMC会议和黑天鹅事件）的显著影响。这些重大经济事件可能会产生类似于
// 大质量物体对时空扭曲的效应。在金融市场中，这种扭曲效应在连续体上运行，可以类比为
// "价格-时间"。

// 为了更好地解释这种扭曲效应，可以使用洛伦兹距离作为欧几里得距离的替代度量。
// 洛伦兹空间的几何结构一开始可能难以可视化，理解它的最佳方式之一是通过涉及
// 2个特征维度(z=2)的例子。就本例而言，我们假设这两个特征是相对强弱指数(RSI)
// 和平均方向指数(ADX)。实际上，最佳特征数量范围在3-8之间，但为简单起见，
// 我们在本例中仅使用2个特征。

// 基本假设:
// (1) 我们可以为给定图表计算RSI和ADX。
// (2) 为简单起见，RSI和ADX的值被假定遵循在0到100范围内的高斯分布。
// (3) 最近的RSI和ADX值可以被视为坐标系的原点，ADX在x轴上，RSI在y轴上。

// 欧几里得空间中的距离:
// 测量历史值与原点处最近点的欧几里得距离将产生类似于图1(如下)的分布。

//                        [RSI]
//                          |                      
//                          |                   
//                          |                 
//                      ...:::....              
//                .:.:::••••••:::•::..             
//              .:•:.:•••::::••::••....::.            
//             ....:••••:••••••••::••:...:•.          
//            ...:.::::::•••:::•••:•••::.:•..          
//            ::•:.:•:•••••••:.:•::::::...:..         
//  |--------.:•••..•••••••:••:...:::•:•:..:..----------[ADX]    
//  0        :•:....:•••••::.:::•••::••:.....            
//           ::....:.:••••••••:•••::••::..:.          
//            .:...:••:::••••••••::•••....:          
//              ::....:.....:•::•••:::::..             
//                ..:..::••..::::..:•:..              
//                    .::..:::.....:                
//                          |            
//                          |                   
//                          |
//                          |
//                         _|_ 0        
//                          
//        图1: 欧几里得空间中的邻域

// 洛伦兹空间中的距离:
// 然而，使用洛伦兹距离测量的相同历史值集将产生不同的分布，类似于图2(如下)。

//                         
//                         [RSI] 
//  ::..                     |                    ..:::  
//   .....                   |                  ......
//    .••••::.               |               :••••••. 
//     .:•••••:.             |            :::••••••.  
//       .•••••:...          |         .::.••••••.    
//         .::•••••::..      |       :..••••••..      
//            .:•••••••::.........::••••••:..         
//              ..::::••••.•••••••.•••••••:.            
//                ...:•••••••.•••••••••::.              
//                  .:..••.••••••.••••..                
//  |---------------.:•••••••••••••••••.---------------[ADX]          
//  0             .:•:•••.••••••.•••••••.                
//              .••••••••••••••••••••••••:.            
//            .:••••••••••::..::.::••••••••:.          
//          .::••••••::.     |       .::•••:::.       
//         .:••••••..        |          :••••••••.     
//       .:••••:...          |           ..•••••••:.   
//     ..:••::..             |              :.•••••••.   
//    .:•....                |               ...::.:••.  
//   ...:..                  |                   :...:••.     
//  :::.                     |                       ..::  
//                          _|_ 0
//
//       图2: 洛伦兹空间中的邻域


// 观察结果:
// (1) 在洛伦兹空间中，两点之间的最短距离不一定是直线，而是测地线曲线。
// (2) 洛伦兹距离的扭曲效应减少了异常值和噪声的整体影响。
// (3) 随着用于比较的最近邻数量的增加，洛伦兹距离与欧几里得距离的差异越来越大。

// ======================
// ==== 自定义类型 ====
// ======================

// 本节使用PineScript的新类型语法定义脚本中使用的重要数据结构。

type Settings
    float source
    int neighborsCount
    int maxBarsBack
    int featureCount
    int colorCompression
    bool showExits
    bool useDynamicExits

type Label
    int long
    int short
    int neutral

type FeatureArrays
    array<float> f1
    array<float> f2
    array<float> f3
    array<float> f4
    array<float> f5

type FeatureSeries
    float f1
    float f2
    float f3
    float f4
    float f5

type MLModel
    int firstBarIndex
    array<int> trainingLabels
    int loopSize
    float lastDistance
    array<float> distancesArray
    array<int> predictionsArray
    int prediction

type FilterSettings 
    bool useVolatilityFilter
    bool useRegimeFilter
    bool useAdxFilter
    float regimeThreshold
    int adxThreshold

type Filter
    bool volatility
    bool regime
    bool adx 

// ==========================
// ==== 辅助函数 ====
// ==========================

series_from(feature_string, _close, _high, _low, _hlc3, f_paramA, f_paramB) =>
    switch feature_string
        "RSI" => ml.n_rsi(_close, f_paramA, f_paramB)
        "WT" => ml.n_wt(_hlc3, f_paramA, f_paramB)
        "CCI" => ml.n_cci(_close, f_paramA, f_paramB)
        "ADX" => ml.n_adx(_high, _low, _close, f_paramA)

get_lorentzian_distance(int i, int featureCount, FeatureSeries featureSeries, FeatureArrays featureArrays) =>
    switch featureCount
        5 => math.log(1+math.abs(featureSeries.f1 - array.get(featureArrays.f1, i))) + 
             math.log(1+math.abs(featureSeries.f2 - array.get(featureArrays.f2, i))) + 
             math.log(1+math.abs(featureSeries.f3 - array.get(featureArrays.f3, i))) + 
             math.log(1+math.abs(featureSeries.f4 - array.get(featureArrays.f4, i))) + 
             math.log(1+math.abs(featureSeries.f5 - array.get(featureArrays.f5, i)))
        4 => math.log(1+math.abs(featureSeries.f1 - array.get(featureArrays.f1, i))) +
             math.log(1+math.abs(featureSeries.f2 - array.get(featureArrays.f2, i))) +
             math.log(1+math.abs(featureSeries.f3 - array.get(featureArrays.f3, i))) +
             math.log(1+math.abs(featureSeries.f4 - array.get(featureArrays.f4, i)))
        3 => math.log(1+math.abs(featureSeries.f1 - array.get(featureArrays.f1, i))) +
             math.log(1+math.abs(featureSeries.f2 - array.get(featureArrays.f2, i))) +
             math.log(1+math.abs(featureSeries.f3 - array.get(featureArrays.f3, i)))
        2 => math.log(1+math.abs(featureSeries.f1 - array.get(featureArrays.f1, i))) +
             math.log(1+math.abs(featureSeries.f2 - array.get(featureArrays.f2, i)))

// ================  
// ==== 输入参数 ==== 
// ================ 

// 设置对象: 通用用户自定义输入
Settings settings = 
 Settings.new(
   input.source(title='数据源', defval=close, group="基本设置", tooltip="输入数据的来源"),
   input.int(title='邻居数量', defval=8, group="基本设置", minval=1, maxval=100, step=1, tooltip="要考虑的邻居数量"),
   input.int(title="最大回溯K线数", defval=2000, group="基本设置"),
   input.int(title="特征数量", defval=5, group="特征工程", minval=2, maxval=5, tooltip="用于ML预测的特征数量"),
   input.int(title="颜色压缩", defval=1, group="基本设置", minval=1, maxval=10, tooltip="调整颜色比例强度的压缩因子"),
   input.bool(title="显示默认出场", defval=false, group="基本设置", tooltip="默认出场在入场信号后精确的4根K线后发生。这对应于模型训练过程中预定义的交易长度。", inline="exits"),
   input.bool(title="使用动态出场", defval=false, group="基本设置", tooltip="动态出场尝试通过基于核回归逻辑动态调整出场阈值来保持利润。", inline="exits")
 )
   
// 交易统计设置
// 注意: 交易统计部分不打算用作适当回测的替代品。它仅用于校准目的。
showTradeStats = input.bool(true, '显示交易统计', tooltip='显示给定配置的交易统计数据。对于优化特征工程部分的设置很有用。这不应替代回测，仅用于校准目的。提前信号翻转表示模型在4根K线结束前改变信号的情况；高值可能表示市场振荡（区间）条件。', group="基本设置")
useWorstCase = input.bool(false, "使用最坏情况估计", tooltip="是否使用回测的最坏情况。此选项可用于创建仅基于收盘价的保守估计，从而避免K线内重绘的影响。此选项假设用户在信号首次出现时不会入场，而是等待K线收盘作为确认。在较大的时间周期上，这可能意味着在大幅走势已经发生后才入场。禁用此选项通常对那些将此指标用作汇合来源并偏好展示自由K线内入场的估计更好。启用此选项可能与传统回测结果更一致。", group="基本设置")

// 用户定义设置的设置对象
FilterSettings filterSettings =
 FilterSettings.new(
   input.bool(title="使用波动率过滤器", defval=true, tooltip="是否使用波动率过滤器。", group="过滤器"),
   input.bool(title="使用市场状态过滤器", defval=true, group="过滤器", inline="regime"),
   input.bool(title="使用ADX过滤器", defval=false, group="过滤器", inline="adx"),
   input.float(title="阈值", defval=-0.1, minval=-10, maxval=10, step=0.1, tooltip="是否使用趋势检测过滤器。检测趋势/区间市场的阈值。", group="过滤器", inline="regime"),
   input.int(title="阈值", defval=20, minval=0, maxval=100, step=1, tooltip="是否使用ADX过滤器。检测趋势/区间市场的阈值。", group="过滤器", inline="adx")
 )

// 过滤ML预测的过滤器对象
Filter filter =
 Filter.new(
   ml.filter_volatility(1, 10, filterSettings.useVolatilityFilter), 
   ml.regime_filter(ohlc4, filterSettings.regimeThreshold, filterSettings.useRegimeFilter),
   ml.filter_adx(settings.source, 14, filterSettings.adxThreshold, filterSettings.useAdxFilter)
  )

// 特征变量: 用于计算特征序列的用户定义输入。
f1_string = input.string(title="特征1", options=["RSI", "WT", "CCI", "ADX"], defval="RSI", inline = "01", tooltip="用于ML预测的第一个特征。", group="特征工程")
f1_paramA = input.int(title="参数A", tooltip="特征1的主要参数。", defval=14, inline = "02", group="特征工程")
f1_paramB = input.int(title="参数B", tooltip="特征1的次要参数（如适用）。", defval=1, inline = "02", group="特征工程")
f2_string = input.string(title="特征2", options=["RSI", "WT", "CCI", "ADX"], defval="WT", inline = "03", tooltip="用于ML预测的第二个特征。", group="特征工程")
f2_paramA = input.int(title="参数A", tooltip="特征2的主要参数。", defval=10, inline = "04", group="特征工程")
f2_paramB = input.int(title="参数B", tooltip="特征2的次要参数（如适用）。", defval=11, inline = "04", group="特征工程")
f3_string = input.string(title="特征3", options=["RSI", "WT", "CCI", "ADX"], defval="CCI", inline = "05", tooltip="用于ML预测的第三个特征。", group="特征工程")
f3_paramA = input.int(title="参数A", tooltip="特征3的主要参数。", defval=20, inline = "06", group="特征工程")
f3_paramB = input.int(title="参数B", tooltip="特征3的次要参数（如适用）。", defval=1, inline = "06", group="特征工程")
f4_string = input.string(title="特征4", options=["RSI", "WT", "CCI", "ADX"], defval="ADX", inline = "07", tooltip="用于ML预测的第四个特征。", group="特征工程")
f4_paramA = input.int(title="参数A", tooltip="特征4的主要参数。", defval=20, inline = "08", group="特征工程")
f4_paramB = input.int(title="参数B", tooltip="特征4的次要参数（如适用）。", defval=2, inline = "08", group="特征工程")
f5_string = input.string(title="特征5", options=["RSI", "WT", "CCI", "ADX"], defval="RSI", inline = "09", tooltip="用于ML预测的第五个特征。", group="特征工程")
f5_paramA = input.int(title="参数A", tooltip="特征5的主要参数。", defval=9, inline = "10", group="特征工程")
f5_paramB = input.int(title="参数B", tooltip="特征5的次要参数（如适用）。", defval=1, inline = "10", group="特征工程")

// 特征序列对象: 基于特征变量计算的特征序列
featureSeries = 
 FeatureSeries.new(
   series_from(f1_string, close, high, low, hlc3, f1_paramA, f1_paramB), // f1
   series_from(f2_string, close, high, low, hlc3, f2_paramA, f2_paramB), // f2 
   series_from(f3_string, close, high, low, hlc3, f3_paramA, f3_paramB), // f3
   series_from(f4_string, close, high, low, hlc3, f4_paramA, f4_paramB), // f4
   series_from(f5_string, close, high, low, hlc3, f5_paramA, f5_paramB)  // f5
 )

// 特征数组变量: 存储为ML优化的特征数组的特征序列
// 注意: 这些数组不能在FeatureArrays对象初始化中动态创建，因此必须提前设置。
var f1Array = array.new_float()
var f2Array = array.new_float()
var f3Array = array.new_float()
var f4Array = array.new_float()
var f5Array = array.new_float()
array.push(f1Array, featureSeries.f1)
array.push(f2Array, featureSeries.f2)
array.push(f3Array, featureSeries.f3)
array.push(f4Array, featureSeries.f4)
array.push(f5Array, featureSeries.f5)

// 特征数组对象: 将计算的特征数组存储到单个对象中
featureArrays = 
 FeatureArrays.new(
  f1Array, // f1
  f2Array, // f2
  f3Array, // f3
  f4Array, // f4
  f5Array  // f5
 )

// 标签对象: 用于将历史数据分类为ML模型的训练数据
Label direction = 
 Label.new(
   long=1, 
   short=-1, 
   neutral=0
  )

// 从常规设置派生
maxBarsBackIndex = last_bar_index >= settings.maxBarsBack ? last_bar_index - settings.maxBarsBack : 0

// EMA设置 
useEmaFilter = input.bool(title="使用EMA过滤器", defval=false, group="过滤器", inline="ema")
emaPeriod = input.int(title="周期", defval=200, minval=1, step=1, group="过滤器", inline="ema", tooltip="EMA过滤器使用的EMA周期。")
isEmaUptrend = useEmaFilter ? close > ta.ema(close, emaPeriod) : true
isEmaDowntrend = useEmaFilter ? close < ta.ema(close, emaPeriod) : true
useSmaFilter = input.bool(title="使用SMA过滤器", defval=false, group="过滤器", inline="sma")
smaPeriod = input.int(title="周期", defval=200, minval=1, step=1, group="过滤器", inline="sma", tooltip="SMA过滤器使用的SMA周期。")
isSmaUptrend = useSmaFilter ? close > ta.sma(close, smaPeriod) : true
isSmaDowntrend = useSmaFilter ? close < ta.sma(close, smaPeriod) : true

// Nadaraya-Watson核回归设置
useKernelFilter = input.bool(true, "使用核过滤交易", group="核设置", inline="kernel")
showKernelEstimate = input.bool(true, "显示核估计", group="核设置", inline="kernel")
useKernelSmoothing = input.bool(false, "增强核平滑", tooltip="使用基于交叉的机制来平滑核颜色变化。这通常会导致较少的颜色转换，并可能导致生成更多的ML入场信号。", inline='1', group='核设置')
h = input.int(8, '回溯窗口', minval=3, tooltip='用于估计的K线数量。这是一个滑动值，代表最近的历史K线。推荐范围：3-50', group="核设置", inline="kernel")
r = input.float(8., '相对权重', step=0.25, tooltip='时间框架的相对权重。当此值接近零时，较长的时间框架将对估计产生更大的影响。当此值接近无穷大时，有理二次核的行为将与高斯核相同。推荐范围：0.25-25', group="核设置", inline="kernel")
x = input.int(25, "回归级别", tooltip='开始回归的K线索引。控制核估计与数据的拟合程度。较小的值拟合更紧密。较大的值拟合更宽松。推荐范围：2-25', group="核设置", inline="kernel")
lag = input.int(2, "滞后", tooltip="交叉检测的滞后。较低的值会导致较早的交叉。推荐范围：1-2", inline='1', group='核设置')

// 显示设置
showBarColors = input.bool(true, "显示K线颜色", tooltip="是否显示K线颜色。", group="显示设置")
showBarPredictions = input.bool(defval = true, title = "显示K线预测值", tooltip = "将显示ML模型对每个K线的评估为整数。", group="显示设置")
useAtrOffset = input.bool(defval = false, title = "使用ATR偏移", tooltip = "将使用ATR偏移而不是K线预测偏移。", group="显示设置")
barPredictionsOffset = input.float(0, "K线预测偏移", minval=0, tooltip="K线预测值相对于K线高点或收盘价的百分比偏移。", group="显示设置")

// =================================
// ==== 下一K线分类 ====
// =================================

// 该模型专门预测未来4根K线的价格走势方向。
// 为避免ML模型复杂化，此值硬编码为4根K线，但未来可能添加对其他训练长度的支持。
src = settings.source
y_train_series = src[4] < src[0] ? direction.short : src[4] > src[0] ? direction.long : direction.neutral
var y_train_array = array.new_int(0)

// 用于ML逻辑的变量
var predictions = array.new_float(0)
var prediction = 0.
var signal = direction.neutral
var distances = array.new_float(0)

array.push(y_train_array, y_train_series)

// =========================
// ==== 核心ML逻辑 ====
// =========================

// 使用洛伦兹距离的近似最近邻搜索:
// 一种确保邻居在时间上均匀分布的最近邻(NN)搜索算法的新变体。

// 在传统的基于KNN的方法中，我们会遍历整个数据集并计算当前K线与数据集中的每个其他K线之间的距离，
// 然后按升序排序这些距离。然后我们取前k个K线，使用它们的标签来确定当前K线的标签。

// 在涉及时间序列数据的实时计算环境中，这种传统的KNN方法存在几个问题:
// - 遍历整个数据集并计算每个历史K线与当前K线之间的距离在计算上是昂贵的。
// - 市场时间序列数据通常是非平稳的，这意味着数据的统计特性随时间稍微变化。
// - 最近邻可能不是最具信息量的，如果最近邻不能代表大多数数据，KNN算法可能会返回较差的结果。

// 之前，用户@capissimo尝试在他的几个基于PineScript的KNN实现中解决其中一些问题，方法是:
// - 使用基于连续最远邻的修改KNN算法来找到一组近似的"最近"邻。
// - 使用滑动窗口方法仅计算当前K线与数据集中最近n个K线之间的距离。

// 在这两种方法中，后者本质上受限于它只考虑整个数据集中最近的K线。

// 前者方法在利用历史价格行为方面具有更多的潜力，但受限于:
// - 突然的"最大"值可能会扰乱估计
// - 可能选择一组近似邻居，这些邻居通过过度采样彼此在时间上不够明显的值，不能代表大多数数据
// - 可能选择太多"远"邻居，这可能导致对价格行为的估计较差

// 为解决这些问题，本指标使用了一种新颖的近似最近邻(ANN)算法。

// 在下面的ANN算法中:
// 1. 该算法按时间顺序遍历数据集，使用模运算符每4个K线执行一次计算。
//    这具有双重目的，减少算法的计算开销，并确保邻居之间的最小时间间隔至少为4个K线。
// 2. 在预测数组和相应的距离数组中同时维护k个相似邻居的列表。
// 3. 当预测数组的大小超过settings.neighborsCount中指定的所需最近邻数量时，
//    算法从预测数组和相应的距离数组中移除第一个邻居。
// 4. lastDistance变量被覆盖为数组中较低25%的距离。这一步通过确保随后新添加的距离值以较慢的速率增加，
//    有助于提高整体准确性。
// 5. 使用洛伦兹距离作为距离度量，以最小化异常值的影响，并考虑由于接近重大经济事件而导致的"价格-时间"扭曲。

lastDistance = -1.0
size = math.min(settings.maxBarsBack-1, array.size(y_train_array)-1)
sizeLoop = math.min(settings.maxBarsBack-1, size)

if bar_index >= maxBarsBackIndex //{
    for i = 0 to sizeLoop //{
        d = get_lorentzian_distance(i, settings.featureCount, featureSeries, featureArrays) 
        if d >= lastDistance and i%4 //{
            lastDistance := d            
            array.push(distances, d)
            array.push(predictions, math.round(array.get(y_train_array, i)))
            if array.size(predictions) > settings.neighborsCount //{
                lastDistance := array.get(distances, math.round(settings.neighborsCount*3/4))
                array.shift(distances)
                array.shift(predictions)
            //}
        //}
    //}
    prediction := array.sum(predictions)
//}

// ============================
// ==== 预测过滤器 ====
// ============================

// 用户定义过滤器: 用于调整ML模型预测频率的过滤器
filter_all = filter.volatility and filter.regime and filter.adx

// 过滤后的信号: 应用用户定义过滤器后，模型对未来价格走势方向的预测
signal := prediction > 0 and filter_all ? direction.long : prediction < 0 and filter_all ? direction.short : nz(signal[1])

// K线计数过滤器: 基于预定义的4根K线持有期的严格过滤器
var int barsHeld = 0
barsHeld := ta.change(signal) ? 0 : barsHeld + 1
isHeldFourBars = barsHeld == 4
isHeldLessThanFourBars = 0 < barsHeld and barsHeld < 4

// 分形过滤器: 从给定时间序列分形/片段中信号的相对出现派生，默认长度为4根K线
isDifferentSignalType = ta.change(signal)
isEarlySignalFlip = ta.change(signal) and (ta.change(signal[1]) or ta.change(signal[2]) or ta.change(signal[3]))
isBuySignal = signal == direction.long and isEmaUptrend and isSmaUptrend
isSellSignal = signal == direction.short and isEmaDowntrend and isSmaDowntrend
isLastSignalBuy = signal[4] == direction.long and isEmaUptrend[4] and isSmaUptrend[4]
isLastSignalSell = signal[4] == direction.short and isEmaDowntrend[4] and isSmaDowntrend[4]
isNewBuySignal = isBuySignal and isDifferentSignalType
isNewSellSignal = isSellSignal and isDifferentSignalType

// 核回归过滤器: 基于使用有理二次核的Nadaraya-Watson核回归的过滤器
// 有关此技术的更多信息，请参阅我的另一个开源指标:
// https://www.tradingview.com/script/AWNvbPRM-Nadaraya-Watson-Rational-Quadratic-Kernel-Non-Repainting/
c_green = color.new(#009988, 20)
c_red = color.new(#CC3311, 20)
transparent = color.new(#000000, 100)
yhat1 = kernels.rationalQuadratic(settings.source, h, r, x)
yhat2 = kernels.gaussian(settings.source, h-lag, x)
kernelEstimate = yhat1
// 核变化率
bool wasBearishRate = yhat1[2] > yhat1[1]
bool wasBullishRate = yhat1[2] < yhat1[1]
bool isBearishRate = yhat1[1] > yhat1
bool isBullishRate = yhat1[1] < yhat1
isBearishChange = isBearishRate and wasBullishRate
isBullishChange = isBullishRate and wasBearishRate
// 核交叉
bool isBullishCrossAlert = ta.crossover(yhat2, yhat1)
bool isBearishCrossAlert = ta.crossunder(yhat2, yhat1) 
bool isBullishSmooth = yhat2 >= yhat1
bool isBearishSmooth = yhat2 <= yhat1
// 核颜色
color colorByCross = isBullishSmooth ? c_green : c_red
color colorByRate = isBullishRate ? c_green : c_red
color plotColor = showKernelEstimate ? (useKernelSmoothing ? colorByCross : colorByRate) : transparent
plot(kernelEstimate, color=plotColor, linewidth=2, title="核回归估计")
// 警报变量
bool alertBullish = useKernelSmoothing ? isBullishCrossAlert : isBullishChange
bool alertBearish = useKernelSmoothing ? isBearishCrossAlert : isBearishChange
// 基于核的看涨和看跌过滤器
isBullish = useKernelFilter ? (useKernelSmoothing ? isBullishSmooth : isBullishRate) : true
isBearish = useKernelFilter ? (useKernelSmoothing ? isBearishSmooth : isBearishRate) : true

// ===========================
// ==== 入场和出场 ====
// ===========================

// 入场条件: ML模型仓位入场的布尔值
startLongTrade = isNewBuySignal and isBullish and isEmaUptrend and isSmaUptrend
startShortTrade = isNewSellSignal and isBearish and isEmaDowntrend and isSmaDowntrend

// 动态出场条件: 基于分形过滤器和核回归过滤器的ML模型仓位出场的布尔值
lastSignalWasBullish = ta.barssince(startLongTrade) < ta.barssince(startShortTrade)
lastSignalWasBearish = ta.barssince(startShortTrade) < ta.barssince(startLongTrade)
barsSinceRedEntry = ta.barssince(startShortTrade)
barsSinceRedExit = ta.barssince(alertBullish)
barsSinceGreenEntry = ta.barssince(startLongTrade)
barsSinceGreenExit = ta.barssince(alertBearish)
isValidShortExit = barsSinceRedExit > barsSinceRedEntry
isValidLongExit = barsSinceGreenExit > barsSinceGreenEntry
endLongTradeDynamic = (isBearishChange and isValidLongExit[1])
endShortTradeDynamic = (isBullishChange and isValidShortExit[1])

// 固定出场条件: 基于K线计数过滤器的ML模型仓位出场的布尔值
endLongTradeStrict = ((isHeldFourBars and isLastSignalBuy) or (isHeldLessThanFourBars and isNewSellSignal and isLastSignalBuy)) and startLongTrade[4]
endShortTradeStrict = ((isHeldFourBars and isLastSignalSell) or (isHeldLessThanFourBars and isNewBuySignal and isLastSignalSell)) and startShortTrade[4]
isDynamicExitValid = not useEmaFilter and not useSmaFilter and not useKernelSmoothing
endLongTrade = settings.useDynamicExits and isDynamicExitValid ? endLongTradeDynamic : endLongTradeStrict 
endShortTrade = settings.useDynamicExits and isDynamicExitValid ? endShortTradeDynamic : endShortTradeStrict

// ================
// ==== 警报 ====
// ================ 

// 单独的入场和出场警报
alertcondition(startLongTrade, title='开仓做多 ▲', message='洛伦兹分类 开仓做多 ▲ | {{ticker}}@{{close}} | ({{interval}})')
alertcondition(endLongTrade, title='平仓做多 ▲', message='洛伦兹分类 平仓做多 ▲ | {{ticker}}@{{close}} | ({{interval}})')
alertcondition(startShortTrade, title='开仓做空 ▼', message='洛伦兹分类 开仓做空  | {{ticker}}@{{close}} | ({{interval}})')
alertcondition(endShortTrade, title='平仓做空 ▼', message='洛伦兹分类 平仓做空 ▼ | {{ticker}}@{{close}} | ({{interval}})')

// 组合的入场和出场警报
alertcondition(startShortTrade or startLongTrade, title='开仓 ▲▼', message='洛伦兹分类 开仓 ▲▼ | {{ticker}}@{{close}} | ({{interval}})')
alertcondition(endShortTrade or endLongTrade, title='平仓 ▲▼', message='洛伦兹分类 平仓  ▲▼ | {{ticker}}@[{{close}}] | ({{interval}})')

// 核估计警报
alertcondition(condition=alertBullish, title='核看涨颜色变化', message='洛伦兹分类 核看涨 ▲ | {{ticker}}@{{close}} | ({{interval}})')
alertcondition(condition=alertBearish, title='核看跌颜色变化', message='洛伦兹分类 核看跌 ▼ | {{ticker}}@{{close}} | ({{interval}})')

// =========================
// ==== 显示信号 ==== 
// =========================

atrSpaced = useAtrOffset ? ta.atr(1) : na
compressionFactor = settings.neighborsCount / settings.colorCompression
c_pred = prediction > 0 ? color.from_gradient(prediction, 0, compressionFactor, #787b86, #009988) : prediction <= 0 ? color.from_gradient(prediction, -compressionFactor, 0, #CC3311, #787b86) : na
c_label = showBarPredictions ? c_pred : na
c_bars = showBarColors ? color.new(c_pred, 50) : na
x_val = bar_index
y_val = useAtrOffset ? prediction > 0 ? high + atrSpaced: low - atrSpaced : prediction > 0 ? high + hl2*barPredictionsOffset/20 : low - hl2*barPredictionsOffset/30
label.new(x_val, y_val, str.tostring(prediction), xloc.bar_index, yloc.price, color.new(color.white, 100), label.style_label_up, c_label, size.normal, text.align_left)
barcolor(showBarColors ? color.new(c_pred, 50) : na)

// ===================== 
// ==== 回测 ====
// =====================

// 以下可用于将信号流式传输到回测适配器
backTestStream = switch 
    startLongTrade => 1
    endLongTrade => 2
    startShortTrade => -1
    endShortTrade => -2
plot(backTestStream, "回测数据流", display=display.none)

// 以下可用于显示实时交易统计信息。这可以是在特征工程期间获取实时反馈的有用机制。这不能替代适当的回测。
// 注意：在此上下文中，"止损"被定义为ML信号在出场信号生成前过早转向的情况。
[totalWins, totalLosses, totalEarlySignalFlips, totalTrades, tradeStatsHeader, winLossRatio, winRate] = ml.backtest(high, low, open, startLongTrade, endLongTrade, startShortTrade, endShortTrade, isEarlySignalFlip, maxBarsBackIndex, bar_index, settings.source, useWorstCase)

// 初始化文字显示
init_text_stats() =>
    var statsText = ""
    statsText

// 更新文字统计信息
update_text_stats(tbl, tradeStatsHeader, totalTrades, totalWins, totalLosses, winLossRatio, winRate, stopLosses) => 
    var text = tradeStatsHeader + "\n" +
               "胜率: " + str.tostring(winRate, '#.#%') + "\n" +
               "交易总数: " + str.tostring(totalTrades, '#') + " (" + str.tostring(totalWins, '#') + "赢|" + str.tostring(totalLosses, '#') + "输)\n" +
               "盈亏比: " + str.tostring(winLossRatio, '0.00') + "\n" +
               "提前信号翻转: " + str.tostring(stopLosses, '#')
    
    label.new(bar_index, high, text, xloc.bar_index, yloc.price, 
             color.new(color.blue, 80), label.style_label_down, 
             color.white, size.normal, text.align_left)

if showTradeStats
    var stats = init_text_stats()
    if barstate.islast
        update_text_stats(stats, tradeStatsHeader, totalTrades, totalWins, totalLosses, winLossRatio, winRate, totalEarlySignalFlips)